/* Import accessibility styles */
@import './styles/accessibility.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Amberglow Design System - All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 32 30% 98%;
    --foreground: 30 20% 15%;

    --card: 32 30% 98%;
    --card-foreground: 30 20% 15%;

    --popover: 32 30% 98%;
    --popover-foreground: 30 20% 15%;

    --primary: 29 95% 57%; /* Amber #FFA726 */
    --primary-foreground: 0 0% 100%;

    --secondary: 28 100% 88%; /* Light amber */
    --secondary-foreground: 30 40% 20%;

    --muted: 28 50% 95%;
    --muted-foreground: 30 20% 45%;

    --accent: 28 100% 88%;
    --accent-foreground: 30 40% 20%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 28 30% 90%;
    --input: 28 30% 90%;
    --ring: 29 95% 57%;

    --radius: 0.75rem;

    --sidebar-background: 32 30% 98%;
    --sidebar-foreground: 30 20% 45%;
    --sidebar-primary: 29 95% 57%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 28 50% 95%;
    --sidebar-accent-foreground: 30 40% 20%;
    --sidebar-border: 28 30% 90%;
    --sidebar-ring: 29 95% 57%;
  }

  .dark {
    --background: 30 15% 8%;
    --foreground: 28 100% 92%;

    --card: 30 15% 10%;
    --card-foreground: 28 100% 92%;

    --popover: 30 15% 10%;
    --popover-foreground: 28 100% 92%;

    --primary: 29 85% 65%; /* Warmer amber for dark mode */
    --primary-foreground: 30 20% 15%;

    --secondary: 30 20% 15%;
    --secondary-foreground: 28 80% 85%;

    --muted: 30 15% 15%;
    --muted-foreground: 28 50% 60%;

    --accent: 30 20% 15%;
    --accent-foreground: 28 80% 85%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 30 15% 20%;
    --input: 30 15% 20%;
    --ring: 29 85% 65%;

    --sidebar-background: 30 15% 8%;
    --sidebar-foreground: 28 50% 60%;
    --sidebar-primary: 29 85% 65%;
    --sidebar-primary-foreground: 30 20% 15%;
    --sidebar-accent: 30 15% 15%;
    --sidebar-accent-foreground: 28 100% 92%;
    --sidebar-border: 30 15% 20%;
    --sidebar-ring: 29 85% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
    background-image:
      radial-gradient(circle at 20% 80%, rgba(255, 167, 38, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 112, 67, 0.1) 0%, transparent 50%);
    min-height: 100vh;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-lora;
  }
}

@layer components {
  /* Enhanced Glass Effects */
  .glass-effect {
    @apply backdrop-blur-sm bg-white/20 border border-white/30 shadow-sm;
  }

  .glass-modern {
    @apply backdrop-blur-md bg-white/40 border border-white/20 shadow-lg;
  }

  .glass-premium {
    @apply backdrop-blur-lg bg-white/50 border border-white/25 shadow-xl;
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .glass-card {
    @apply backdrop-blur-md bg-white/60 border border-white/30 shadow-lg;
    box-shadow:
      0 8px 32px rgba(255, 167, 38, 0.1),
      0 4px 16px rgba(255, 112, 67, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  /* Enhanced Glow Effects */
  .warm-glow {
    box-shadow: 0 0 20px rgba(255, 167, 38, 0.3);
  }

  .warm-glow-soft {
    box-shadow:
      0 4px 20px rgba(255, 167, 38, 0.15),
      0 2px 8px rgba(255, 112, 67, 0.1);
  }

  .warm-glow-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .warm-glow-hover:hover {
    box-shadow:
      0 8px 32px rgba(255, 167, 38, 0.2),
      0 4px 16px rgba(255, 112, 67, 0.15),
      0 0 0 1px rgba(255, 167, 38, 0.1);
    transform: translateY(-2px);
  }

  /* Enhanced Text Gradients */
  .text-gradient {
    background: linear-gradient(135deg, #ffa726, #ff7043);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
    padding: 0.1em 0;
    display: inline-block;
  }

  .text-gradient-warm {
    background: linear-gradient(135deg, #ff7043 0%, #ffa726 50%, #ffb74d 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
    padding: 0.1em 0;
    display: inline-block;
  }

  /* Modern Gradient Backgrounds */
  .gradient-amber {
    background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
  }

  .gradient-warm {
    background: linear-gradient(135deg, #ff7043 0%, #ffa726 100%);
  }

  .gradient-warm-subtle {
    background: linear-gradient(135deg,
      rgba(255, 167, 38, 0.1) 0%,
      rgba(255, 112, 67, 0.05) 100%);
  }

  /* Enhanced Card Styles */
  .card-modern {
    @apply rounded-xl border border-amber-200/30 bg-white/80 backdrop-blur-sm;
    box-shadow:
      0 4px 20px rgba(255, 167, 38, 0.08),
      0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .card-elevated {
    @apply rounded-2xl border border-amber-200/40 bg-white/90 backdrop-blur-md;
    box-shadow:
      0 8px 32px rgba(255, 167, 38, 0.12),
      0 4px 16px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  /* Modern Typography */
  .text-modern {
    @apply leading-relaxed tracking-wide;
  }

  .text-elegant {
    @apply font-lora leading-loose tracking-wide;
  }

  .heading-modern {
    @apply font-lora font-semibold tracking-tight;
  }

  /* Modern Spacing */
  .section-padding {
    @apply px-6 py-8 md:px-8 md:py-12;
  }

  .content-spacing {
    @apply space-y-6 md:space-y-8;
  }

  .form-spacing {
    @apply space-y-6;
  }

  /* Modern Borders */
  .border-modern {
    @apply border border-amber-200/40 rounded-xl;
  }

  .border-elegant {
    @apply border border-amber-200/30 rounded-2xl;
  }

  /* Enhanced Focus States */
  .focus-modern {
    @apply focus:outline-none focus:ring-2 focus:ring-amber-500/50 focus:border-amber-400 transition-all duration-200;
  }

  .focus-elegant {
    @apply focus:outline-none focus:ring-2 focus:ring-amber-500/30 focus:border-amber-300 focus:shadow-lg transition-all duration-300;
  }

  /* Modern Button Styles */
  .btn-modern {
    @apply px-6 py-3 rounded-xl font-medium transition-all duration-200 ease-in-out;
  }

  .btn-elegant {
    @apply px-8 py-4 rounded-2xl font-medium transition-all duration-300 ease-out;
  }

  /* Hover Animations */
  .hover-lift {
    @apply transition-all duration-300 ease-out;
  }

  .hover-lift:hover {
    @apply -translate-y-1 shadow-lg;
  }

  .hover-glow {
    @apply transition-all duration-300 ease-out;
  }

  .hover-glow:hover {
    @apply shadow-xl;
    box-shadow:
      0 8px 32px rgba(255, 167, 38, 0.2),
      0 4px 16px rgba(255, 112, 67, 0.15);
  }

  /* Floating Animation */
  .float {
    animation: float 6s ease-in-out infinite;
  }

  .fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  .slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .scale-in {
    animation: scaleIn 0.4s ease-out;
  }

  .float-delayed {
    animation: float 6s ease-in-out infinite;
    animation-delay: 2s;
  }

  /* Enhanced Loading States */
  .loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
  }

  .loading-pulse {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* Staggered Animations */
  .stagger-1 { animation-delay: 0.1s; }
  .stagger-2 { animation-delay: 0.2s; }
  .stagger-3 { animation-delay: 0.3s; }
  .stagger-4 { animation-delay: 0.4s; }
  .stagger-5 { animation-delay: 0.5s; }
}

/* Enhanced Keyframe Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 167, 38, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(255, 167, 38, 0.5);
  }
}

/* Enhanced pulse animation for New Entry Button */
@keyframes new-entry-pulse {
  0%, 100% {
    box-shadow: 0 0 15px rgba(255, 167, 38, 0.4), 0 0 30px rgba(255, 167, 38, 0.2);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 25px rgba(255, 167, 38, 0.6), 0 0 40px rgba(255, 167, 38, 0.3);
    transform: scale(1.02);
  }
}

/* New Entry Button with continuous pulse that pauses on hover */
.new-entry-button {
  animation: new-entry-pulse 2s ease-in-out infinite !important;
}

.new-entry-button:hover {
  animation: none !important;
  transform: scale(1.05) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-warm-50;
}

::-webkit-scrollbar-thumb {
  @apply bg-amber-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-amber-400;
}
