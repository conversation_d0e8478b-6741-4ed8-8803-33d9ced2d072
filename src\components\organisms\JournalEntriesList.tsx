/**
 * Journal Entries List Organism
 * Complete list component for displaying multiple journal entries
 * Optimized with virtualization and performance monitoring
 */

import React from 'react';
import { Book, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/atoms/LoadingSpinner';
import { AccessibleLoadMoreButton } from '@/components/atoms/LoadMoreButton';
import { EmptyState } from '@/components/molecules/EmptyState';
import { JournalEntryCard } from '@/components/organisms/JournalEntryCard';
import { cn } from '@/utils/utils';
import { FormattedJournalEntry, BaseComponentProps } from '@/types';
import { useServiceWithLoading } from '@/hooks/useServiceWithLoading';
import {
  useVirtualization,
  useStableCallback,
  useIntersectionObserver,
} from '@/utils/performance.utils';

interface JournalEntriesListProps extends BaseComponentProps {
  /** Array of journal entries */
  entries: FormattedJournalEntry[];
  /** Loading state from service integration */
  loadingState: ReturnType<typeof useServiceWithLoading>['loadingState'];
  /** Whether to show entry actions */
  showEntryActions?: boolean;
  /** List layout variant */
  layout?: 'grid' | 'list';
  /** Entry size */
  entrySize?: 'sm' | 'md' | 'lg';
  /** Maximum content lines per entry */
  entryContentMaxLines?: number;
  /** Enable virtualization for large lists */
  enableVirtualization?: boolean;
  /** Container height for virtualization */
  containerHeight?: number;
  /** Handlers */
  onCreateEntry?: () => void;
  onEditEntry?: (entryId: string) => void;
  onDeleteEntry?: (entryId: string) => void;
  onViewEntry?: (entryId: string) => void;
  onRetry?: () => void;
  /** Pagination props */
  pagination?: {
    /** Whether there are more entries to load */
    hasMore: boolean;
    /** Whether load more is in progress */
    isLoadingMore: boolean;
    /** Handler for loading more entries */
    onLoadMore: () => void;
    /** Total number of loaded entries */
    loadedCount?: number;
    /** Total number of available entries */
    totalCount?: number;
  };
}

const layoutClasses = {
  grid: 'grid gap-8 md:grid-cols-2 lg:grid-cols-3',
  list: 'content-spacing',
};

const JournalEntriesListComponent = ({
  entries,
  loadingState,
  showEntryActions = false,
  layout = 'list',
  entrySize = 'md',
  entryContentMaxLines,
  enableVirtualization = false,
  containerHeight = 600,
  onCreateEntry,
  onEditEntry,
  onDeleteEntry,
  onViewEntry,
  onRetry,
  pagination,
  className,
  testId,
}: JournalEntriesListProps) => {
  // Performance monitoring - temporarily disabled
  // useRenderCount('JournalEntriesList');

  // Stable callback handlers
  const handleCreateEntry = useStableCallback(
    () => {
      if (onCreateEntry) onCreateEntry();
    },
    [onCreateEntry],
    'JournalEntriesList.handleCreateEntry'
  );

  const handleEditEntry = useStableCallback(
    (entryId: string) => {
      if (onEditEntry) onEditEntry(entryId);
    },
    [onEditEntry],
    'JournalEntriesList.handleEditEntry'
  );

  const handleDeleteEntry = useStableCallback(
    (entryId: string) => {
      if (onDeleteEntry) onDeleteEntry(entryId);
    },
    [onDeleteEntry],
    'JournalEntriesList.handleDeleteEntry'
  );

  const handleViewEntry = useStableCallback(
    (entryId: string) => {
      if (onViewEntry) onViewEntry(entryId);
    },
    [onViewEntry],
    'JournalEntriesList.handleViewEntry'
  );

  // Virtualization for large lists
  const shouldUseVirtualization = enableVirtualization && entries.length > 20;
  const itemHeight = layout === 'grid' ? 300 : 200; // Estimated heights

  const virtualization = useVirtualization(
    entries,
    itemHeight,
    containerHeight,
    5 // overscan
  );

  // Determine which entries to render
  const entriesToRender = shouldUseVirtualization ? virtualization.visibleItems : entries;
  // Loading state - errors are now handled by the centralized error handling system
  if (loadingState.isLoading) {
    return (
      <div className={cn('py-8', className)} data-testid={testId}>
        <LoadingSpinner size="lg" message={loadingState.message || 'Loading your entries...'} />
      </div>
    );
  }

  // Empty state
  if (entries.length === 0) {
    return (
      <div className={cn(className)} data-testid={testId}>
        <EmptyState
          icon={Book}
          title="No entries yet"
          description="Start your mindful journaling journey today"
          actionText={onCreateEntry ? 'Write your first entry' : undefined}
          onAction={onCreateEntry}
          size="lg"
        />
      </div>
    );
  }

  // Entries list
  return (
    <div className={cn(className)} data-testid={testId}>
      {/* Header with create button */}
      {onCreateEntry && (
        <div className="flex justify-between items-center mb-12 fade-in">
          <div className="space-y-2">
            <h2 className="text-3xl heading-modern text-gradient-warm">Your Journal</h2>
            <p className="text-muted-foreground text-modern">Reflecting on your journey of growth</p>
          </div>
          <Button
            onClick={onCreateEntry}
            className={cn(
              // Base styling with larger size
              "px-8 py-4 text-lg font-semibold rounded-xl",
              "bg-gradient-to-r from-amber-500 to-orange-500",
              "hover:from-amber-600 hover:to-orange-600",
              "text-white shadow-lg",

              // Enhanced hover and focus effects
              "transition-all duration-300 ease-out",
              "hover:shadow-xl hover:scale-105",
              "focus:ring-4 focus:ring-amber-300 focus:ring-opacity-50",
              "active:scale-95",

              // Subtle glow animation
              "relative overflow-hidden",
              "before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent",
              "before:translate-x-[-100%] before:transition-transform before:duration-1000",
              "hover:before:translate-x-[100%]",

              // Continuous pulse animation that pauses on hover
              "animate-pulse-glow hover:animate-none"
            )}
          >
            <Plus className="w-5 h-5 mr-3" />
            New Entry
          </Button>
        </div>
      )}

      {/* Entries */}
      {shouldUseVirtualization ? (
        <div
          className="relative overflow-auto"
          style={{ height: containerHeight }}
          onScroll={e => virtualization.setScrollTop(e.currentTarget.scrollTop)}
        >
          <div style={{ height: virtualization.totalHeight, position: 'relative' }}>
            <div
              style={{
                transform: `translateY(${virtualization.offsetY}px)`,
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
              }}
              className={layoutClasses[layout]}
            >
              {entriesToRender.map((entry, index) => (
                <JournalEntryCard
                  key={entry.id}
                  entry={entry}
                  size={entrySize}
                  showActions={showEntryActions}
                  contentMaxLines={entryContentMaxLines}
                  onEdit={handleEditEntry}
                  onDelete={handleDeleteEntry}
                  onView={handleViewEntry}
                />
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div className={layoutClasses[layout]}>
          {entriesToRender.map((entry, index) => (
            <JournalEntryCard
              key={entry.id}
              entry={entry}
              size={entrySize}
              showActions={showEntryActions}
              contentMaxLines={entryContentMaxLines}
              onEdit={handleEditEntry}
              onDelete={handleDeleteEntry}
              onView={handleViewEntry}
              className="slide-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            />
          ))}
        </div>
      )}

      {/* Load More Button */}
      {pagination && pagination.hasMore && (
        <div className="mt-8">
          <AccessibleLoadMoreButton
            onClick={pagination.onLoadMore}
            isLoading={pagination.isLoadingMore}
            disabled={pagination.isLoadingMore}
            loadedCount={pagination.loadedCount}
            totalCount={pagination.totalCount}
            hasMore={pagination.hasMore}
            text="Load More Entries"
            loadingText="Loading more entries..."
            size="lg"
            fullWidth={false}
            testId="journal-load-more-button"
          />
        </div>
      )}
    </div>
  );
};

// Memoized component for performance optimization
export const JournalEntriesList = React.memo(
  JournalEntriesListComponent,
  (prevProps, nextProps) => {
    // Custom comparison for better performance
    return (
      prevProps.entries.length === nextProps.entries.length &&
      prevProps.entries.every(
        (entry, index) =>
          entry.id === nextProps.entries[index]?.id &&
          entry.updated_at === nextProps.entries[index]?.updated_at
      ) &&
      prevProps.loadingState.isLoading === nextProps.loadingState.isLoading &&
      prevProps.loadingState.error === nextProps.loadingState.error &&
      prevProps.showEntryActions === nextProps.showEntryActions &&
      prevProps.layout === nextProps.layout &&
      prevProps.entrySize === nextProps.entrySize &&
      prevProps.entryContentMaxLines === nextProps.entryContentMaxLines &&
      prevProps.enableVirtualization === nextProps.enableVirtualization &&
      prevProps.containerHeight === nextProps.containerHeight &&
      prevProps.pagination?.hasMore === nextProps.pagination?.hasMore &&
      prevProps.pagination?.isLoadingMore === nextProps.pagination?.isLoadingMore &&
      prevProps.pagination?.loadedCount === nextProps.pagination?.loadedCount &&
      prevProps.pagination?.totalCount === nextProps.pagination?.totalCount
    );
  }
);
