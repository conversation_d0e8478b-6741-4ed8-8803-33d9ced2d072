/**
 * Journal Page
 * Dedicated page component for viewing and managing journal entries
 */

import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import PageLayout from '@/components/layout/PageLayout';
import { JournalEntriesPresenter } from '@/components/features/JournalEntriesPresenter';
import { ConfirmationModal } from '@/components/ui/confirmation-modal';
import { EditJournalEntryModal } from '@/components/features/EditJournalEntryModal';
import { toast } from 'sonner';

import { useAuth } from '@/contexts/AuthContext';
import { useJournalPagination } from '@/hooks/useJournalPagination';
import { useDeleteJournalEntry, useUpdateJournalEntry } from '@/hooks/queries/useJournalQueries';
import { FormattedJournalEntry, EmotionType, UpdateJournalEntryPayload } from '@/types';

const Journal = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, loading: authLoading } = useAuth();
  const {
    loadedEntries: entries,
    isInitialLoading,
    isLoadingMore,
    hasMoreEntries,
    error,
    loadMore,
    refresh,
    updateEntry,
    removeEntry,
  } = useJournalPagination({
    pageSize: 10,
    enablePrefetch: true,
  });

  // React Query mutation for deleting entries
  const deleteEntryMutation = useDeleteJournalEntry({
    onSuccess: (_, entryId) => {
      console.log('🎉 React Query delete mutation successful for entry:', entryId);
      console.log('📊 Entries before local removal:', entries.length);

      // Update local pagination state for immediate UI feedback
      // This works in conjunction with React Query cache invalidation
      removeEntry(entryId);
      console.log('✅ Entry deleted successfully from both cache and local state');

      // Close the modal
      setDeleteModal({
        isOpen: false,
        entryId: null,
        entryTitle: '',
        isLoading: false,
      });
    },
    onError: (error) => {
      console.error('❌ Delete mutation failed:', error);
      toast.error('Failed to delete journal entry');
      setDeleteModal(prev => ({ ...prev, isLoading: false }));
    },
  });

  // React Query mutation for updating entries
  const updateEntryMutation = useUpdateJournalEntry({
    onSuccess: (data, { id }) => {
      console.log('🎉 React Query update mutation successful for entry:', id);

      // The mutation already handles React Query cache updates
      // We just need to update our local pagination state for immediate UI feedback
      updateEntry(id, data);

      console.log('📊 Entry updated in local state');
    },
    onError: (error) => {
      console.error('❌ Update mutation failed:', error);
      toast.error('Failed to update journal entry');
    },
  });

  // Modal states
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    entryId: string | null;
    entryTitle: string;
    isLoading: boolean;
  }>({
    isOpen: false,
    entryId: null,
    entryTitle: '',
    isLoading: false,
  });

  const [editModal, setEditModal] = useState<{
    isOpen: boolean;
    entry: FormattedJournalEntry | null;
  }>({
    isOpen: false,
    entry: null,
  });

  console.log('📚 Journal page render:', {
    user: user?.id,
    authLoading,
    entriesCount: entries.length,
    isInitialLoading,
    isLoadingMore,
    hasMoreEntries,
  });

  // Scroll to top when page loads
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Redirect to auth if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      navigate('/auth');
    }
  }, [user, authLoading, navigate]);

  // Refresh data when navigating from Write page to ensure new entries appear
  useEffect(() => {
    // Check if we're coming from the Write page (via navigation state or referrer)
    const fromWritePage = location.state?.from === '/write' ||
                          document.referrer.includes('/write') ||
                          location.state?.refresh === true;

    if (fromWritePage && user) {
      console.log('📚 Detected navigation from Write page, refreshing journal entries');
      refresh();
    }
  }, [location.state, user]); // Removed refresh - it's stable enough for this use case

  // Note: Entries are automatically loaded by useJournalPagination when user changes

  // Handler for navigating to write page
  const handleCreateEntry = () => {
    navigate('/write');
  };

  // Handler for entry actions (edit/delete)
  const handleEntryAction = async (entryId: string, action: 'edit' | 'delete') => {
    const entry = entries.find(e => e.id === entryId);
    if (!entry) {
      toast.error('Entry not found');
      return;
    }

    if (action === 'delete') {
      // Open delete confirmation modal
      setDeleteModal({
        isOpen: true,
        entryId,
        entryTitle: entry.title,
        isLoading: false,
      });
    } else if (action === 'edit') {
      // Open edit modal
      setEditModal({
        isOpen: true,
        entry,
      });
    }
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!deleteModal.entryId || !user) return;

    console.log('🗑️ Delete confirmation triggered:', {
      entryId: deleteModal.entryId,
      userId: user.id,
      currentEntriesCount: entries.length,
      mutationStatus: deleteEntryMutation.status
    });

    setDeleteModal(prev => ({ ...prev, isLoading: true }));

    // Use React Query mutation for proper cache management
    deleteEntryMutation.mutate(deleteModal.entryId);
  };

  // Handle delete cancellation
  const handleDeleteCancel = () => {
    setDeleteModal({
      isOpen: false,
      entryId: null,
      entryTitle: '',
      isLoading: false,
    });
  };

  // Handle edit save
  const handleEditSave = (updatedEntry: FormattedJournalEntry) => {
    console.log('📝 Saving updated entry:', {
      id: updatedEntry.id,
      title: updatedEntry.title,
      hasChanges: true
    });

    // Optimistic update for immediate UI feedback
    updateEntry(updatedEntry.id, updatedEntry);

    // Close modal immediately for better UX
    setEditModal({
      isOpen: false,
      entry: null,
    });

    // Make API call to persist changes
    const updatePayload: UpdateJournalEntryPayload = {
      title: updatedEntry.title,
      content: updatedEntry.content,
      emotion: updatedEntry.emotion as EmotionType,
      mood_score: updatedEntry.mood_score,
      ...(updatedEntry.ai_reflection && { ai_reflection: updatedEntry.ai_reflection }),
      ...(updatedEntry.ai_summary && { ai_summary: updatedEntry.ai_summary }),
      ...(updatedEntry.ai_emotion && { ai_emotion: updatedEntry.ai_emotion }),
      ...(updatedEntry.ai_encouragement && { ai_encouragement: updatedEntry.ai_encouragement }),
      ...(updatedEntry.ai_reflection_question && { ai_reflection_question: updatedEntry.ai_reflection_question }),
    };

    console.log('🔄 Triggering database update mutation with payload:', {
      id: updatedEntry.id,
      payload: updatePayload,
      mutationStatus: updateEntryMutation.status
    });

    updateEntryMutation.mutate({
      id: updatedEntry.id,
      payload: updatePayload
    });
  };

  // Handle edit cancellation
  const handleEditCancel = () => {
    setEditModal({
      isOpen: false,
      entry: null,
    });
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-500 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated (will redirect)
  if (!user) {
    return null;
  }

  return (
    <PageLayout>
        {/* Debug section - temporary for troubleshooting */}
        {process.env.NODE_ENV === 'development' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 m-4">
            <h3 className="font-semibold text-yellow-800 mb-2">Debug Info (Development Only)</h3>
            <div className="text-sm text-yellow-700 space-y-1">
              <p>User ID: {user?.id}</p>
              <p>Entries Count: {entries.length}</p>
              <p>Initial Loading: {isInitialLoading.toString()}</p>
              <p>Loading More: {isLoadingMore.toString()}</p>
              <p>Has More: {hasMoreEntries.toString()}</p>
              <p>Auth Loading: {authLoading.toString()}</p>
              <p>Error: {error?.message || 'None'}</p>
              <p>Delete Mutation Status: {deleteEntryMutation.status}</p>

              <div className="mt-2 space-x-2">
                <Button onClick={refresh} variant="outline" size="sm">
                  🔄 Refresh Entries
                </Button>
                <Button
                  onClick={() =>
                    console.log('Current state:', {
                      user: user.id,
                      entries,
                      isInitialLoading,
                      isLoadingMore,
                      hasMoreEntries,
                      error
                    })
                  }
                  variant="outline"
                  size="sm"
                >
                  📊 Log State
                </Button>
                {hasMoreEntries && (
                  <Button onClick={loadMore} variant="outline" size="sm" disabled={isLoadingMore}>
                    ⬇️ Load More
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}

        <JournalEntriesPresenter
          entries={entries}
          loadingState={{
            isLoading: isInitialLoading,
            ...(isInitialLoading && { message: 'Loading your journal entries...' }),
          }}
          onCreateEntry={handleCreateEntry}
          onEntryAction={handleEntryAction}
          pagination={{
            hasMore: hasMoreEntries,
            isLoadingMore: isLoadingMore,
            onLoadMore: loadMore,
            loadedCount: entries.length,
          }}
        />

        {/* Delete Confirmation Modal */}
        <ConfirmationModal
          isOpen={deleteModal.isOpen}
          onClose={handleDeleteCancel}
          onConfirm={handleDeleteConfirm}
          title="Delete Journal Entry"
          description={`Are you sure you want to delete "${deleteModal.entryTitle}"? This action cannot be undone.`}
          confirmText="Delete"
          cancelText="Cancel"
          variant="destructive"
          isLoading={deleteEntryMutation.isPending}
          icon="trash"
        />

        {/* Edit Journal Entry Modal */}
        <EditJournalEntryModal
          isOpen={editModal.isOpen}
          onClose={handleEditCancel}
          onSave={handleEditSave}
          entry={editModal.entry}
        />
    </PageLayout>
  );
};

export default Journal;
